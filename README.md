## WorX With Neha — Performance Marketing Site

This is a Vite + React + TypeScript project styled with Tailwind CSS and shadcn/ui components.

### Getting Started

Prereqs: Node.js and npm (or bun/pnpm/yarn).

```sh
# Install dependencies
npm install

# Start dev server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Tech Stack

- Vite
- React 18
- TypeScript
- Tailwind CSS
- shadcn/ui

### Project Structure

- `src/` app code
- `public/` static assets (includes `favicon.svg`)

### Notes

- Favicon has been migrated to an SVG at `public/favicon.svg` with a W mark.
- Any previous third‑party branding and tags have been removed from the codebase.
