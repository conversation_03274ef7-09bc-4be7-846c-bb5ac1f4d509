import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ArrowRight, TrendingUp, Target, BarChart3, Download, Rocket, Mail } from "lucide-react";
import { useState } from "react";
import heroBackground from "@/assets/hero-bg.jpg";
import nehaPhoto from "@/assets/neha-photo.jpg";

const HeroSection = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    website: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-background">
      {/* Background with Dashboard Mockups */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `url(${heroBackground})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      />

      {/* Floating Dashboard Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 animate-bounce">
          <div className="p-4 bg-primary/10 rounded-xl backdrop-blur-sm border border-primary/20">
            <div className="text-primary font-bold text-lg">+247%</div>
            <div className="text-xs text-muted-foreground">ROI Increase</div>
          </div>
        </div>
        <div className="absolute top-32 right-20 animate-bounce" style={{ animationDelay: '0.5s' }}>
          <div className="p-4 bg-accent/10 rounded-xl backdrop-blur-sm border border-accent/20">
            <div className="text-accent font-bold text-lg">$2.4M</div>
            <div className="text-xs text-muted-foreground">Revenue Generated</div>
          </div>
        </div>
        <div className="absolute bottom-32 left-20 animate-bounce" style={{ animationDelay: '1s' }}>
          <div className="p-4 bg-primary/10 rounded-xl backdrop-blur-sm border border-primary/20">
            <BarChart3 className="w-6 h-6 text-primary mb-2" />
            <div className="text-xs text-muted-foreground">Live Analytics</div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-screen py-20">
          {/* Left Column - Content */}
          <div className="space-y-8 animate-fade-in">
            {/* Main Headline */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight text-foreground">
              👉 From Clicks to Clients:{" "}
              <span className="text-primary">Performance Marketing</span>{" "}
              That Delivers{" "}
              <span className="text-primary">ROI</span>
            </h1>

            {/* Subheadline */}
            <p className="text-lg md:text-xl text-muted-foreground leading-relaxed animate-fade-in animate-delay-200">
              I help businesses in <strong>real estate, healthcare, manufacturing, and services</strong> generate
              consistent, qualified leads through data-driven ad campaigns, conversion funnels, automation,
              and ROI-focused reporting.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 animate-fade-in animate-delay-300">
              <Button
                className="bg-accent hover:bg-accent-hover text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-glow hover:scale-105 group"
                size="lg"
              >
                <Rocket className="mr-2 w-5 h-5" />
                Get My Free Funnel Audit 🚀
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Button>

              <Button
                variant="outline"
                className="border-primary text-primary hover:bg-primary hover:text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 group"
                size="lg"
              >
                <Download className="mr-2 w-5 h-5" />
                📊 Download ROI Guide
              </Button>
            </div>

            {/* Inline Lead Magnet Form */}
            <div className="bg-card border border-border rounded-2xl p-6 shadow-card animate-fade-in animate-delay-400">
              <div className="flex items-center gap-2 mb-4">
                <Mail className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold text-foreground">
                  📩 Claim Your Free Audit & Funnel Strategy
                </h3>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid md:grid-cols-3 gap-4">
                  <Input
                    type="text"
                    name="name"
                    placeholder="Your Name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="bg-background border-border"
                    required
                  />
                  <Input
                    type="email"
                    name="email"
                    placeholder="Email Address"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="bg-background border-border"
                    required
                  />
                  <Input
                    type="url"
                    name="website"
                    placeholder="Website URL"
                    value={formData.website}
                    onChange={handleInputChange}
                    className="bg-background border-border"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full bg-primary hover:bg-primary-hover text-white font-semibold py-3 rounded-xl transition-all duration-300"
                >
                  Send Me My Free Strategy
                </Button>
              </form>
            </div>
          </div>

          {/* Right Column - Professional Photo */}
          <div className="relative animate-fade-in animate-delay-500">
            <div className="relative max-w-md mx-auto">
              {/* Background gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 rounded-3xl transform rotate-6"></div>

              {/* Photo */}
              <img
                src={nehaPhoto}
                alt="Neha - Performance Marketing Expert"
                className="relative w-full rounded-3xl shadow-2xl"
              />

              {/* Floating achievement badges */}
              <div className="absolute -top-4 -right-4 bg-background shadow-card rounded-xl p-4 border border-border">
                <div className="text-2xl font-bold text-primary">4+</div>
                <div className="text-sm text-muted-foreground">Years</div>
              </div>

              <div className="absolute -bottom-4 -left-4 bg-background shadow-card rounded-xl p-4 border border-border">
                <div className="text-2xl font-bold text-accent">200%</div>
                <div className="text-sm text-muted-foreground">Avg ROI</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;