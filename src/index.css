@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance Marketing Portfolio Design System */

@layer base {
  :root {
    /* Light theme - Premium performance marketing palette */
    --background: 0 0% 99%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    /* Primary - Growth Green */
    --primary: 142 70% 45%; /* emerald */
    --primary-foreground: 0 0% 100%;
    --primary-hover: 142 70% 38%;

    /* Secondary - Soft Gray */
    --secondary: 210 20% 96%;
    --secondary-foreground: 215 25% 25%;

    /* Muted - Light Gray */
    --muted: 210 20% 96%;
    --muted-foreground: 215 15% 55%;

    /* Accent - Lime Green */
    --accent: 95 85% 45%;
    --accent-foreground: 0 0% 100%;
    --accent-hover: 95 85% 38%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 215 20% 90%;
    --input: 215 20% 90%;
    --ring: 142 70% 45%;

    --radius: 0.75rem;

    /* Custom design tokens */
    --gradient-primary: linear-gradient(135deg, hsl(142 70% 45%), hsl(95 85% 45%));
    --gradient-hero: linear-gradient(135deg, hsl(142 70% 45% / 0.08), hsl(95 85% 45% / 0.08));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(142 70% 98%));
    
    --shadow-card: 0 4px 20px -2px hsl(215 25% 15% / 0.08);
    --shadow-hover: 0 8px 40px -4px hsl(215 25% 15% / 0.15);
    --shadow-glow: 0 0 32px hsl(142 70% 45% / 0.35);

    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Typography scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Dark - Vibrant accents for contrast (green) */
    --primary: 142 70% 55%;
    --primary-foreground: 0 0% 100%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 95 85% 40%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 70% 50%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  /* Custom utility classes */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-hero {
    background: var(--gradient-hero);
  }

  .gradient-card {
    background: var(--gradient-card);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-hover {
    box-shadow: var(--shadow-hover);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-bounce {
    transition: var(--transition-bounce);
  }

  /* Animation utilities */
  .animate-fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-up {
    transform: translateY(20px);
    opacity: 0;
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-scale-in {
    transform: scale(0.95);
    opacity: 0;
    animation: scaleIn 0.4s ease-out forwards;
  }

  /* Staggered animation delays */
  .animate-delay-100 { animation-delay: 0.1s; }
  .animate-delay-200 { animation-delay: 0.2s; }
  .animate-delay-300 { animation-delay: 0.3s; }
  .animate-delay-400 { animation-delay: 0.4s; }
  .animate-delay-500 { animation-delay: 0.5s; }
}

@layer components {
  /* Hero button variant */
  .btn-hero {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-glow hover:scale-105;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-hover hover:-translate-y-1;
  }

  /* Gradient text */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  to {
    transform: scale(1);
    opacity: 1;
  }
}