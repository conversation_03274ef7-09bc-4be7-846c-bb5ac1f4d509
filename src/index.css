@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance Marketing Portfolio Design System */

@layer base {
  :root {
    /* WorX With Neha Brand Palette */
    --background: 210 20% 98%; /* Light Grey #F5F7FA */
    --foreground: 0 0% 20%; /* Charcoal #333333 */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 20%; /* Charcoal #333333 */

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 20%; /* Charcoal #333333 */

    /* Primary - Teal */
    --primary: 180 100% 25%; /* Teal #008080 */
    --primary-foreground: 0 0% 100%;
    --primary-hover: 180 100% 20%;

    /* Secondary - Navy */
    --secondary: 220 45% 20%; /* Navy #1B2A49 */
    --secondary-foreground: 0 0% 100%;

    /* Muted - Light Gray */
    --muted: 210 20% 96%; /* Light Grey variations */
    --muted-foreground: 0 0% 45%;

    /* Accent - Coral */
    --accent: 14 100% 63%; /* Coral #FF7043 */
    --accent-foreground: 0 0% 100%;
    --accent-hover: 14 100% 55%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 90%;
    --input: 210 20% 90%;
    --ring: 180 100% 25%; /* Teal */

    --radius: 0.75rem;

    /* Custom design tokens - Updated for new brand */
    --gradient-primary: linear-gradient(135deg, hsl(180 100% 25%), hsl(14 100% 63%));
    --gradient-hero: linear-gradient(135deg, hsl(180 100% 25% / 0.08), hsl(14 100% 63% / 0.08));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(210 20% 98%));
    
    --shadow-card: 0 4px 20px -2px hsl(215 25% 15% / 0.08);
    --shadow-hover: 0 8px 40px -4px hsl(215 25% 15% / 0.15);
    --shadow-glow: 0 0 32px hsl(142 70% 45% / 0.35);

    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Typography scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 220 45% 8%; /* Dark Navy variation */
    --foreground: 210 20% 95%;

    --card: 220 45% 10%;
    --card-foreground: 210 20% 95%;

    --popover: 220 45% 10%;
    --popover-foreground: 210 20% 95%;

    /* Dark - Teal primary */
    --primary: 180 100% 35%; /* Brighter Teal for dark mode */
    --primary-foreground: 0 0% 100%;

    --secondary: 220 45% 15%; /* Dark Navy */
    --secondary-foreground: 210 20% 95%;

    --muted: 220 45% 15%;
    --muted-foreground: 210 20% 65%;

    --accent: 14 100% 65%; /* Brighter Coral for dark mode */
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 45% 20%;
    --input: 220 45% 20%;
    --ring: 180 100% 35%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  /* Custom utility classes */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-hero {
    background: var(--gradient-hero);
  }

  .gradient-card {
    background: var(--gradient-card);
  }

  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-hover {
    box-shadow: var(--shadow-hover);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-bounce {
    transition: var(--transition-bounce);
  }

  /* Animation utilities */
  .animate-fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-up {
    transform: translateY(20px);
    opacity: 0;
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-scale-in {
    transform: scale(0.95);
    opacity: 0;
    animation: scaleIn 0.4s ease-out forwards;
  }

  /* Staggered animation delays */
  .animate-delay-100 { animation-delay: 0.1s; }
  .animate-delay-200 { animation-delay: 0.2s; }
  .animate-delay-300 { animation-delay: 0.3s; }
  .animate-delay-400 { animation-delay: 0.4s; }
  .animate-delay-500 { animation-delay: 0.5s; }
}

@layer components {
  /* Hero button variant - Updated for Coral CTA */
  .btn-hero {
    @apply bg-accent hover:bg-accent-hover text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-glow hover:scale-105;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-hover hover:-translate-y-1;
  }

  /* Gradient text - Updated for Teal/Coral gradient */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Navigation styles for navy header */
  .nav-link {
    @apply text-white hover:text-primary transition-colors font-medium;
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  to {
    transform: scale(1);
    opacity: 1;
  }
}